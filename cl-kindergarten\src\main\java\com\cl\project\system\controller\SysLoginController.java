package com.cl.project.system.controller;

import java.util.List;
import java.util.Set;

import cn.dev33.satoken.annotation.SaIgnore;
import com.cl.framework.security.LoginBody;
import com.cl.framework.security.service.SysAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.cl.common.constant.Constants;
import com.cl.framework.security.LoginUser;
import com.cl.framework.security.service.SysPermissionService;
import com.cl.framework.security.service.TokenService;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.project.system.domain.SysMenu;
import com.cl.project.system.domain.SysUser;
import com.cl.project.system.service.ISysMenuService;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysAuthService authService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    /**
     * 登录方法
     *
     * @param loginBody 登陆信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = authService.login(loginBody);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 登出方法
     *
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/logout")
    public AjaxResult logout() {
        AjaxResult ajax = AjaxResult.success();
        authService.logout();
        return ajax;
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        LoginUser loginUser = tokenService.getLoginUser();
        // 用户信息
        SysUser user = loginUser.getUser();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(user);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
