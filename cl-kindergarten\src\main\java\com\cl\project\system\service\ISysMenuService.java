package com.cl.project.system.service;

import java.util.List;
import java.util.Set;
import com.cl.framework.web.domain.TreeSelect;
import com.cl.project.system.domain.SysMenu;
import com.cl.project.system.domain.SysUser;
import com.cl.project.system.domain.vo.RouterVo;

/**
 * 菜单 业务层
 * 
 * <AUTHOR>
 */
public interface ISysMenuService
{
    /**
     * 根据用户查询系统菜单列表
     * 
     * @param user 用户
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuList(SysUser user);

    /**
     * 根据用户查询系统菜单列表
     * 
     * @param menu 菜单信息
     * @param user 用户
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuList(SysMenu menu, SysUser user);

    /**
     * 查询所有菜单
     * @return
     */
    List<SysMenu> listMenusBySuperFlag(Integer superFlag);

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据租户的权限模版ID查询权限
     *
     * @param tempId 租户的权限模版ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByTempId(String tempId);

    /**
     * 根据用户ID查询菜单树信息
     * 
     * @param user 用户
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeByUserId(SysUser user);

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Integer> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端路由所需要的菜单
     * 
     * @param menus 菜单列表
     * @return 路由列表
     */
    List<RouterVo> buildMenus(List<SysMenu> menus);

    /**
     * 构建前端所需要树结构
     * 
     * @param menus 菜单列表
     * @return 树结构列表
     */
    List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus);

    /**
     * 根据菜单ID查询信息
     * 
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     * 
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByMenuId(Long menuId);

    /**
     * 查询菜单是否存在角色
     * 
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkMenuExistRole(Long menuId);

    /**
     * 新增保存菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    int insertMenu(SysMenu menu);

    /**
     * 修改保存菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    int updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    String checkMenuNameUnique(SysMenu menu);

    List<Integer> listCheckedMenusByTempId(String tempId);
}
