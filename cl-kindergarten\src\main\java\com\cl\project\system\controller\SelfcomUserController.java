package com.cl.project.system.controller;

import java.util.List;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.cl.common.constant.UserConstants;
import com.cl.common.utils.SecurityUtils;
import com.cl.common.utils.StringUtils;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.framework.security.LoginUser;
import com.cl.framework.security.service.TokenService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.framework.web.page.TableDataInfo;
import com.cl.project.system.domain.SysUser;
import com.cl.project.system.service.ISysPostService;
import com.cl.project.system.service.ISysRoleService;
import com.cl.project.system.service.ISysUserService;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/selfcom/user")
public class SelfcomUserController extends BaseController {

  @Autowired
  private ISysUserService userService;

  @Autowired
  private ISysRoleService roleService;

  @Autowired
  private ISysPostService postService;

  @Autowired
  private TokenService tokenService;

  /**
   * 获取用户列表
   */
  @SaCheckPermission("selfcom:user:list")
  @GetMapping("/list")
  public TableDataInfo list(SysUser user) {
    user.setComId(SecurityUtils.getCurrComId());
    startPage();
    List<SysUser> list = userService.selectUserList(user);
    return getDataTable(list);
  }

  @Log(title = "用户管理", businessType = BusinessType.EXPORT)
  @SaCheckPermission("selfcom:user:export")
  @GetMapping("/export")
  public AjaxResult export(SysUser user) {
    List<SysUser> list = userService.selectUserList(user);
    ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
    return util.exportExcel(list, "用户数据");
  }

  @Log(title = "用户管理", businessType = BusinessType.IMPORT)
  @SaCheckPermission("selfcom:user:import")
  @PostMapping("/importData")
  public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
    ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
    List<SysUser> userList = util.importExcel(file.getInputStream());
    LoginUser loginUser = tokenService.getLoginUser();
    String operName = loginUser.getUsername();
    String message = userService.importUser(userList, updateSupport, operName);
    return AjaxResult.success(message);
  }

  @GetMapping("/importTemplate")
  public AjaxResult importTemplate() {
    ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
    return util.importTemplateExcel("用户数据");
  }

  /**
   * 根据用户编号获取详细信息
   */
  @SaCheckPermission("selfcom:user:query")
  @GetMapping(value = {"/", "/{userId}"})
  public AjaxResult getInfo(@PathVariable(value = "userId", required = false) String userId) {
    Long userIdc = StringUtils.isBlank(userId) ? SecurityUtils.getCurrUserId() : Long.valueOf(userId);
    AjaxResult ajax = AjaxResult.success();
//    ajax.put("posts", postService.selectPostAllByCom(SecurityUtils.getCurrComId()));
    if (StringUtils.isNotNull(userIdc)) {
      ajax.put(AjaxResult.DATA_TAG, userService.selectUserById(userIdc));
//      ajax.put("postIds", postService.selectPostListByUserId(userIdc));
      ajax.put("roleIds", roleService.selectRoleListByUserId(userIdc));
    }
    ajax.put(AjaxResult.DATA_TAG, userService.selectUserById(userIdc));

    return ajax;
  }

  /**
   * 新增用户
   */
  @SaCheckPermission("selfcom:user:add")
  @Log(title = "用户管理", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@Validated @RequestBody SysUser user) {
    user.setComId(SecurityUtils.getCurrComId());
    if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user))) {
      return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，该登录账号已存在");
    } else if (UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
      return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
    } else if (UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
      return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
    }
    user.setCreateBy(SecurityUtils.getUsername());
    user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
    user.setComId(SecurityUtils.getCurrComId());
    user.setAdminFlag(0);
    return toAjax(userService.insertUser(user));
  }

  /**
   * 修改用户
   */
  @SaCheckPermission("selfcom:user:edit")
  @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@Validated @RequestBody SysUser user) {
    // 手机号也就是账号
    user.setPhonenumber(user.getUserName());
    userService.checkUserAllowed(user);
    if (UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
      return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
    }
/*    else if (UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
      return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
    }*/
    user.setUpdateBy(SecurityUtils.getUsername());
    return toAjax(userService.updateUser(user));
  }

  /**
   * 删除用户
   */
  @SaCheckPermission("selfcom:user:remove")
  @Log(title = "用户管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{userIds}")
  public AjaxResult remove(@PathVariable Long[] userIds) {

    return toAjax(userService.deleteUserByIds(userIds));
  }

  /**
   * 重置密码
   */
  @SaCheckPermission("selfcom:user:edit")
  @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping("/resetPwd")
  public AjaxResult resetPwd(@RequestBody SysUser user) {
    userService.checkUserAllowed(user);
    user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
    user.setUpdateBy(SecurityUtils.getUsername());
    return toAjax(userService.resetPwd(user));
  }

  /**
   * 状态修改
   */
  @SaCheckPermission("selfcom:user:edit")
  @Log(title = "用户管理", businessType = BusinessType.UPDATE)
  @PutMapping("/changeStatus")
  public AjaxResult changeStatus(@RequestBody SysUser user) {
    user.setUpdateBy(SecurityUtils.getUsername());
    return toAjax(userService.updateUserStatus(user));
  }
}