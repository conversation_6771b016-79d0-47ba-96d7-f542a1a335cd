package com.cl.project.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.common.constant.UserConstants;
import com.cl.common.exception.CustomException;
import com.cl.common.utils.SecurityUtils;
import com.cl.common.utils.StringUtils;
import com.cl.framework.aspectj.lang.annotation.DataScope;
import com.cl.project.system.domain.SysPost;
import com.cl.project.system.domain.SysRole;
import com.cl.project.system.domain.SysUser;
import com.cl.project.system.domain.SysUserPost;
import com.cl.project.system.domain.SysUserRole;
import com.cl.project.system.mapper.SysPostMapper;
import com.cl.project.system.mapper.SysRoleMapper;
import com.cl.project.system.mapper.SysUserMapper;
import com.cl.project.system.mapper.SysUserPostMapper;
import com.cl.project.system.mapper.SysUserRoleMapper;
import com.cl.project.system.service.ISysConfigService;
import com.cl.project.system.service.ISysUserService;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {

  private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

  @Autowired
  private SysUserMapper userMapper;

  @Autowired
  private SysRoleMapper roleMapper;

  @Autowired
  private SysPostMapper postMapper;

  @Autowired
  private SysUserRoleMapper userRoleMapper;

  @Autowired
  private SysUserPostMapper userPostMapper;

  @Autowired
  private ISysConfigService configService;

  /**
   * 根据条件分页查询用户列表
   *
   * @param user 用户信息
   * @return 用户信息集合信息
   */
  @Override
  @DataScope(deptAlias = "d", userAlias = "u")
  public List<SysUser> selectUserList(SysUser user) {
    return userMapper.selectUserList(user);
  }

  /**
   * 通过用户名查询用户
   *
   * @param userName 用户名
   * @return 用户对象信息
   */
  @Override
  public SysUser selectUserByUserName(String comId, String userName) {
    return userMapper.selectUserByUserName(comId, userName);
  }

  /**
   * 通过用户ID查询用户
   *
   * @param userId 用户ID
   * @return 用户对象信息
   */
  @Override
  public SysUser selectUserById(Long userId) {
    return userMapper.selectUserById(userId);
  }

  /**
   * 查询用户所属角色组
   *
   * @param userName 用户名
   * @return 结果
   */
  @Override
  public String selectUserRoleGroup(String userName, String comId) {
    List<SysRole> list = roleMapper.selectRolesByUserName(userName, comId);
    StringBuffer idsStr = new StringBuffer();
    for (SysRole role : list) {
      idsStr.append(role.getRoleName()).append(",");
    }
    if (StringUtils.isNotEmpty(idsStr.toString())) {
      return idsStr.substring(0, idsStr.length() - 1);
    }
    return idsStr.toString();
  }

  /**
   * 查询用户所属岗位组
   *
   * @param userName 用户名
   * @return 结果
   */
  @Override
  public String selectUserPostGroup(String userName, String comId) {
    List<SysPost> list = postMapper.selectPostsByUserName(userName, comId);
    StringBuffer idsStr = new StringBuffer();
    for (SysPost post : list) {
      idsStr.append(post.getPostName()).append(",");
    }
    if (StringUtils.isNotEmpty(idsStr.toString())) {
      return idsStr.substring(0, idsStr.length() - 1);
    }
    return idsStr.toString();
  }

  /**
   * 校验用户名称是否唯一
   *
   * @param user 用户信息
   * @return 结果
   */
  @Override
  public String checkUserNameUnique(SysUser user) {
    int count = userMapper.checkUserNameUnique(user.getComId(), user.getUserName());
    if (count > 0) {
      return UserConstants.NOT_UNIQUE;
    }
    return UserConstants.UNIQUE;
  }

  /**
   * 校验用户名称是否唯一
   *
   * @param user 用户信息
   */
  @Override
  public String checkPhoneUnique(SysUser user) {
    Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
    SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
    if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
      return UserConstants.NOT_UNIQUE;
    }
    return UserConstants.UNIQUE;
  }

  /**
   * 校验email是否唯一
   *
   * @param user 用户信息
   */
  @Override
  public String checkEmailUnique(SysUser user) {
    Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
    SysUser info = userMapper.checkEmailUnique(user.getEmail());
    if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
      return UserConstants.NOT_UNIQUE;
    }
    return UserConstants.UNIQUE;
  }

  /**
   * 校验用户是否允许操作
   *
   * @param user 用户信息
   */
  @Override
  public void checkUserAllowed(SysUser user) {

    // 数据库中查询的当前操作的用户数据
    SysUser userDB = userMapper.selectUserById(user.getUserId());

    SysUser loginUser = SecurityUtils.getSysUser();
    // 如果登录用户是平台超级管理员，则允许随意操作
    if (SecurityUtils.isSuperAdmin(loginUser)) {
      if (SecurityUtils.isSuperAdmin(userDB) && user.isStop()) {
        throw new CustomException("不能停用平台超级管理员账号");
      }
      return;
    }
    // 登录用户是公司管理员(分两层写清晰一些)
    if (SecurityUtils.isComAdmin(loginUser)) {
      // 被操作用户也是管理员账号，并且操作是停用
      if (SecurityUtils.isComAdmin(userDB) && user.isStop()) {
        throw new CustomException("不能停用管理员账号");
      }
    }
    // 只能操作自己公司的数据
    if (!loginUser.getComId().equals(userDB.getComId())) {
      throw new CustomException("无权操作");
    }

    //其他情况通过注解的方式进行权限控制
  }

  /**
   * 新增保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  @Override
  @Transactional
  public int insertUser(SysUser user) {
    // 新增用户信息
    int rows = userMapper.insertUser(user);
    // 新增用户岗位关联
    insertUserPost(user);
    // 新增用户与角色管理
    insertUserRole(user);
    return rows;
  }

  /**
   * 修改保存用户信息
   *
   * @param user 用户信息
   * @return 结果
   */
  @Override
  @Transactional
  public int updateUser(SysUser user) {
    Long userId = user.getUserId();
    // 删除用户与角色关联
    userRoleMapper.deleteUserRoleByUserId(userId);
    // 新增用户与角色管理
    insertUserRole(user);
    // 删除用户与岗位关联
//    userPostMapper.deleteUserPostByUserId(userId);
    // 新增用户与岗位管理
//    insertUserPost(user);
    return userMapper.updateUser(user);
  }

  /**
   * 修改用户状态
   *
   * @param user 用户信息
   * @return 结果
   */
  @Override
  public int updateUserStatus(SysUser user) {
    SysUser sysUser = userMapper.selectUserById(user.getUserId());
    if (SecurityUtils.isSuperAdmin(sysUser) || SecurityUtils.isComAdmin(sysUser)) {
        throw new CustomException("不能禁用管理员账号");
    }

    return userMapper.updateUser(user);
  }

  /**
   * 修改用户基本信息
   *
   * @param user 用户信息
   * @return 结果
   */
  @Override
  public int updateUserProfile(SysUser user) {
    return userMapper.updateUser(user);
  }

  /**
   * 修改用户头像
   *
   * @param userName 用户名
   * @param avatar 头像地址
   * @return 结果
   */
  @Override
  public boolean updateUserAvatar(String userName, String avatar) {
    return userMapper.updateUserAvatar(userName, avatar) > 0;
  }

  /**
   * 重置用户密码
   *
   * @param user 用户信息
   * @return 结果
   */
  @Override
  public int resetPwd(SysUser user) {
    return userMapper.updateUser(user);
  }

  /**
   * 重置用户密码
   *
   * @param userName 用户名
   * @param password 密码
   * @return 结果
   */
  @Override
  public int resetUserPwd(String userName, String password) {
      if (SecurityUtils.isSuperAdmin() || SecurityUtils.isSuperAdmin()) {
          throw new CustomException("无权重置密码");
      }
    return userMapper.resetUserPwd(userName, password);
  }

  /**
   * 新增用户角色信息
   *
   * @param user 用户对象
   */
  public void insertUserRole(SysUser user) {
    Long[] roles = user.getRoleIds();
    if (StringUtils.isNotNull(roles)) {
      // 新增用户与角色管理
      List<SysUserRole> list = new ArrayList<SysUserRole>();
      for (Long roleId : roles) {
        SysUserRole ur = new SysUserRole();
        ur.setUserId(user.getUserId());
        ur.setRoleId(roleId);
        list.add(ur);
      }
      if (list.size() > 0) {
        userRoleMapper.batchUserRole(list);
      }
    }
  }

  /**
   * 新增用户岗位信息
   *
   * @param user 用户对象
   */
  public void insertUserPost(SysUser user) {
    Long[] posts = user.getPostIds();
    if (StringUtils.isNotNull(posts)) {
      // 新增用户与岗位管理
      List<SysUserPost> list = new ArrayList<SysUserPost>();
      for (Long postId : posts) {
        SysUserPost up = new SysUserPost();
        up.setUserId(user.getUserId());
        up.setPostId(postId);
        list.add(up);
      }
      if (list.size() > 0) {
        userPostMapper.batchUserPost(list);
      }
    }
  }

  /**
   * 通过用户ID删除用户
   *
   * @param userId 用户ID
   * @return 结果
   */
  @Override
  public int deleteUserById(Long userId) {
    // 删除用户与角色关联
    userRoleMapper.deleteUserRoleByUserId(userId);
    // 删除用户与岗位表
    userPostMapper.deleteUserPostByUserId(userId);
    return userMapper.deleteUserById(userId);
  }

  /**
   * 批量删除用户信息
   *
   * @param userIds 需要删除的用户ID
   * @return 结果
   */
  @Override
  public int deleteUserByIds(Long[] userIds) {
    for (Long userId : userIds) {
      SysUser sysUser = userMapper.selectUserById(userId);
      if (SecurityUtils.isSuperAdmin(sysUser) || SecurityUtils.isComAdmin(sysUser)) {
          throw new CustomException("不能删除管理员账号");
      }

    }
    return userMapper.deleteUserByIds(userIds);
  }

  /**
   * 导入用户数据
   *
   * @param userList 用户数据列表
   * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
   * @param operName 操作用户
   * @return 结果
   */
  @Override
  public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
    if (StringUtils.isNull(userList) || userList.isEmpty()) {
      throw new CustomException("导入用户数据不能为空！");
    }
    int successNum = 0;
    int failureNum = 0;
    StringBuilder successMsg = new StringBuilder();
    StringBuilder failureMsg = new StringBuilder();
    String password = configService.selectConfigByKey("sys.user.initPassword");
    for (SysUser user : userList) {
      try {
        // 验证是否存在这个用户
        SysUser u = userMapper.selectUserByUserName(user.getComId(), user.getUserName());
        if (StringUtils.isNull(u)) {
          user.setPassword(SecurityUtils.encryptPassword(password));
          user.setCreateBy(operName);
          this.insertUser(user);
          successNum++;
          successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
        } else if (isUpdateSupport) {
          user.setUpdateBy(operName);
          this.updateUser(user);
          successNum++;
          successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
        } else {
          failureNum++;
          failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
        }
      } catch (Exception e) {
        failureNum++;
        String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
        failureMsg.append(msg + e.getMessage());
        log.error(msg, e);
      }
    }
    if (failureNum > 0) {
      failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
      throw new CustomException(failureMsg.toString());
    } else {
      successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
    }
    return successMsg.toString();
  }

}
