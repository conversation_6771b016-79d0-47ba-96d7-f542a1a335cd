<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.system.mapper.SysUserMapper">

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="comId"        column="com_id"        />
		<result property="adminFlag"    column="admin_flag"        />
		<result property="superAdminFlag"    column="super_admin_flag"        />
		<association property="dept"    column="dept_id" javaType="SysDept" resultMap="deptResult" />
		<association property="com"    column="com_id" javaType="SysCompany" resultMap="companyResult" />
		<collection  property="roles"   javaType="java.util.List"        resultMap="RoleResult" />
	</resultMap>
	
	<resultMap id="deptResult" type="SysDept">
		<id     property="deptId"   column="dept_id"     />
		<result property="parentId" column="parent_id"   />
		<result property="deptName" column="dept_name"   />
		<result property="leader"   column="leader"      />
		<result property="status"   column="dept_status" />
	</resultMap>

	<resultMap id="companyResult" type="SysCompany">
		<id     property="id"   column="com_id"     />
		<result property="companyName" column="company_name"   />
		<result property="comCode" column="com_code"   />
		<result property="activeFlag" column="active_flag"   />
		<result property="activeTime" column="active_time"   />
		<result property="tempId" column="temp_id"   />
	</resultMap>
	
	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"     column="data_scope"    />
		<result property="status"       column="role_status"    />
	</resultMap>
	
	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password,
        u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.com_id,u.admin_flag,u.super_admin_flag,
        d.dept_id, d.parent_id, d.dept_name, d.leader, d.status as dept_status,
		c.id as com_id, c.company_name, c.com_code, c.active_flag, c.temp_id,c.active_time,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
			left join sys_company c on u.com_id = c.id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>
    
    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.com_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber,
		u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.admin_flag, u.super_admin_flag,
		d.dept_id, d.parent_id, d.dept_name, d.leader, d.status as dept_status,
		c.id as com_id, c.company_name, c.com_code, c.active_flag, c.temp_id, c.active_time
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join sys_company c on u.com_id = c.id
		where u.del_flag = '0'
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
		</if>
		<if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET (#{deptId},ancestors) ))
		</if>
		<if test="comId != null and comId != ''">
			AND u.com_id = #{comId}
		</if>
		<if test="adminFlag != null and adminFlag != ''">
			AND u.admin_flag = #{adminFlag}
		</if>
		<if test="superAdminFlag != null and superAdminFlag != ''">
			AND u.super_admin_flag = #{superAdminFlag}
		</if>
		<if test="queryRoleId != null and queryRoleId != ''">
			AND #{queryRoleId} IN (select r.role_id
			from sys_role r
			left join sys_user_role ur on ur.role_id = r.role_id
			where ur.user_id = u.user_id)
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName}
		<if test="comId != null and comId != ''">
			AND u.com_id = #{comId}
		</if>
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user
		<where>
			<if test="comId != null and comId != ''">
				and com_id = #{comId}
			</if>
			<if test="userName != null and userName != ''">
				and user_name = #{userName}
			</if>
			and del_flag = 0
		</where>
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber}
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email}
	</select>
	
	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
			<if test="comId != null and comId != ''">com_id,</if>
			<if test="adminFlag != null and adminFlag != ''">admin_flag,</if>
			<if test="superAdminFlag != null and superAdminFlag != ''">super_admin_flag,</if>
		create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="comId != null and comId != ''">#{comId},</if>
			<if test="adminFlag != null and adminFlag != ''">#{adminFlag},</if>
			<if test="superAdminFlag != null and superAdminFlag != ''">#{superAdminFlag},</if>
		sysdate()
 		)
	</insert>
	
	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="email != null and email != ''">email = #{email},</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
			<if test="adminFlag != null">admin_flag = #{adminFlag},</if>
			<if test="superAdminFlag != null">super_admin_flag = #{superAdminFlag},</if>
			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
 		delete from sys_user where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>

	<select id="getComIdByPhone" parameterType="java.lang.String" resultType="java.lang.String">
		select com_id
        from sys_user
        where phonenumber = #{phonenumber}
	</select>

	<select id="getBasicInfoByPhone" parameterType="java.lang.String" resultMap="SysUserResult">
		select u.user_id, u.user_name, u.nick_name, u.phonenumber, u.com_id
        from sys_user u
        where u.phonenumber = #{phonenumber}
        and u.status = 0
        and u.del_flag = 0
	</select>

	<select id="getComAdmin" parameterType="java.lang.String" resultMap="SysUserResult">
		select u.user_id, u.user_name, u.nick_name, u.phonenumber, u.com_id
        from sys_user u
        where u.status = 0
        and u.del_flag = 0
        and u.admin_flag = 1
        and u.com_id = #{comId}
	</select>
</mapper> 