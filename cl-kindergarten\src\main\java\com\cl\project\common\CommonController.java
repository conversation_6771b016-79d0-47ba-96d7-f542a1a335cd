package com.cl.project.common;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.dev33.satoken.annotation.SaIgnore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.cl.common.constant.Constants;
import com.cl.common.utils.StringUtils;
import com.cl.common.utils.file.FileUploadUtils;
import com.cl.common.utils.file.FileUtils;
import com.cl.framework.config.ClConfig;
import com.cl.framework.config.ServerConfig;
import com.cl.framework.web.domain.AjaxResult;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
public class CommonController {

  private static final Logger log = LoggerFactory.getLogger(CommonController.class);

  @Autowired
  private ServerConfig serverConfig;

  @Autowired
  private HttpServletResponse response;

  /**
   * 通用下载请求
   *
   * @param fileName 文件名称
   * @param delete 是否删除
   */
  @SaIgnore
  @GetMapping("common/download")
  public void fileDownload(String fileName, Boolean delete, HttpServletResponse response,
      HttpServletRequest request) {
    try {
      if (!FileUtils.isValidFilename(fileName)) {
        throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
      }
      String realFileName =
          System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
      String filePath = ClConfig.getDownloadPath() + fileName;

      response.setCharacterEncoding("utf-8");
      response.setContentType("multipart/form-data");
      response.setHeader("Content-Disposition",
          "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
      FileUtils.writeBytes(filePath, response.getOutputStream());
      if (delete) {
        FileUtils.deleteFile(filePath);
      }
    } catch (Exception e) {
      log.error("下载文件失败", e);
    }
  }

  /**
   * 通用上传请求
   */
  @PostMapping("/common/upload")
  public AjaxResult uploadFile(MultipartFile file) throws Exception {
    try {
      // 上传文件路径
      String filePath = ClConfig.getUploadPath();
      // 上传并返回新文件名称
      String fileName = FileUploadUtils.upload(filePath, file);
      String url = serverConfig.getUrl() + fileName;
      AjaxResult ajax = AjaxResult.success();
      ajax.put("fileName", fileName);
      ajax.put("url", url);
      return ajax;
    } catch (Exception e) {
      return AjaxResult.error(e.getMessage());
    }
  }

  /**
   * 本地资源通用下载
   */
  @SaIgnore
  @GetMapping("/common/download/resource")
  public void resourceDownload(String name, HttpServletRequest request,
      HttpServletResponse response) throws Exception {
    // 本地资源路径
    String localPath = ClConfig.getProfile();
    // 数据库资源地址
    String downloadPath = localPath + StringUtils.substringAfter(name, Constants.RESOURCE_PREFIX);
    // 下载名称
    String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
    response.setCharacterEncoding("utf-8");
    response.setContentType("multipart/form-data");
    response.setHeader("Content-Disposition",
        "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, downloadName));
    FileUtils.writeBytes(downloadPath, response.getOutputStream());
  }

  /**
   * 本地资源通用下载
   */
 /* @GetMapping("/common/download/print")
  public void downloadPrint(String name)
      throws Exception {
    // 本地资源路径
    String localPath = ClConfig.getProfile();
//    String name = "clodop.exe";
    FileUtils.downLoadFile(response, localPath, name);
  }*/
}
