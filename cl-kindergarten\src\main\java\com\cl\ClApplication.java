package com.cl;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class ClApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(ClApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  启动成功，Thanks for cl   ლ(´ڡ`ლ)ﾞ ");
    }
}
